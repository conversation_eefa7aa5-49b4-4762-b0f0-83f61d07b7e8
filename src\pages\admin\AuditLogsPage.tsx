import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Breadcrumbs,
  Link,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  AlertTitle,
  Avatar,
  Divider,
  Tabs,
  Tab,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TablePagination,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar,
  Collapse,
  Stack
} from '@mui/material';
import {
  ArrowBack,
  FullscreenExit,
  FullscreenIcon,
  History,
  Security,
  Person,
  Settings,
  Warning,
  Error,
  Info,
  CheckCircle,
  Search,
  FilterList,
  Download,
  Refresh,
  ExpandMore,
  ExpandLess,
  Visibility,
  Edit,
  Delete,
  Login,
  Logout,
  AdminPanelSettings,
  Storage,
  Api,
  Shield,
  Computer,
  PhoneAndroid,
  Web,
  LocationOn,
  Schedule,
  Event,
  Assessment
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import PageTitle from '../../components/UI/PageTitle';

interface AuditLog {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  userEmail: string;
  action: string;
  category: 'authentication' | 'user_management' | 'data_access' | 'system_config' | 'security' | 'api_access';
  severity: 'low' | 'medium' | 'high' | 'critical';
  resource: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  location: string;
  success: boolean;
  metadata?: Record<string, any>;
}

interface AuditStats {
  totalLogs: number;
  todayLogs: number;
  failedAttempts: number;
  criticalEvents: number;
  uniqueUsers: number;
  topActions: { action: string; count: number }[];
}

const AuditLogsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [isMaximized, setIsMaximized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterSeverity, setFilterSeverity] = useState('all');
  const [filterDateRange, setFilterDateRange] = useState('today');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [openDetailsDialog, setOpenDetailsDialog] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const [auditStats] = useState<AuditStats>({
    totalLogs: 45678,
    todayLogs: 234,
    failedAttempts: 12,
    criticalEvents: 3,
    uniqueUsers: 156,
    topActions: [
      { action: 'User Login', count: 1234 },
      { action: 'Data Access', count: 987 },
      { action: 'Settings Change', count: 456 },
      { action: 'User Creation', count: 234 },
      { action: 'API Call', count: 189 }
    ]
  });

  const [auditLogs] = useState<AuditLog[]>([
    {
      id: '1',
      timestamp: '2024-01-15 14:30:25',
      userId: 'user_123',
      userName: 'John Doe',
      userEmail: '<EMAIL>',
      action: 'User Login',
      category: 'authentication',
      severity: 'low',
      resource: '/auth/login',
      details: 'Successful login attempt',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'New York, US',
      success: true,
      metadata: {
        sessionId: 'sess_abc123',
        loginMethod: 'password',
        deviceType: 'desktop'
      }
    },
    {
      id: '2',
      timestamp: '2024-01-15 14:25:10',
      userId: 'admin_456',
      userName: 'Admin User',
      userEmail: '<EMAIL>',
      action: 'User Account Suspended',
      category: 'user_management',
      severity: 'high',
      resource: '/admin/users/suspend',
      details: 'Suspended user account due to policy violation',
      ipAddress: '*********',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      location: 'San Francisco, US',
      success: true,
      metadata: {
        targetUserId: 'user_789',
        reason: 'Policy violation',
        duration: '7 days'
      }
    },
    {
      id: '3',
      timestamp: '2024-01-15 14:20:45',
      userId: 'user_789',
      userName: 'Jane Smith',
      userEmail: '<EMAIL>',
      action: 'Failed Login Attempt',
      category: 'authentication',
      severity: 'medium',
      resource: '/auth/login',
      details: 'Invalid password provided',
      ipAddress: '************',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
      location: 'London, UK',
      success: false,
      metadata: {
        attemptCount: 3,
        lockoutTriggered: false,
        deviceType: 'mobile'
      }
    },
    {
      id: '4',
      timestamp: '2024-01-15 14:15:30',
      userId: 'system',
      userName: 'System',
      userEmail: '<EMAIL>',
      action: 'Database Backup',
      category: 'system_config',
      severity: 'low',
      resource: '/system/backup',
      details: 'Automated daily database backup completed successfully',
      ipAddress: '127.0.0.1',
      userAgent: 'DataStatPro-BackupService/1.0',
      location: 'Server Location',
      success: true,
      metadata: {
        backupSize: '2.3GB',
        duration: '45 minutes',
        backupType: 'full'
      }
    },
    {
      id: '5',
      timestamp: '2024-01-15 14:10:15',
      userId: 'user_456',
      userName: 'Bob Johnson',
      userEmail: '<EMAIL>',
      action: 'Dataset Access',
      category: 'data_access',
      severity: 'medium',
      resource: '/datasets/sensitive-data-001',
      details: 'Accessed sensitive dataset for analysis',
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
      location: 'Toronto, CA',
      success: true,
      metadata: {
        datasetId: 'ds_001',
        accessType: 'read',
        recordsAccessed: 15000
      }
    },
    {
      id: '6',
      timestamp: '2024-01-15 14:05:00',
      userId: 'admin_123',
      userName: 'Super Admin',
      userEmail: '<EMAIL>',
      action: 'Security Policy Updated',
      category: 'security',
      severity: 'critical',
      resource: '/admin/security/policies',
      details: 'Updated password complexity requirements',
      ipAddress: '*********',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'Seattle, US',
      success: true,
      metadata: {
        policyType: 'password',
        changes: ['minLength: 12', 'requireSpecialChars: true'],
        affectedUsers: 'all'
      }
    },
    {
      id: '7',
      timestamp: '2024-01-15 14:00:30',
      userId: 'api_client_001',
      userName: 'API Client',
      userEmail: '<EMAIL>',
      action: 'API Rate Limit Exceeded',
      category: 'api_access',
      severity: 'medium',
      resource: '/api/v1/datasets',
      details: 'API client exceeded rate limit of 1000 requests per hour',
      ipAddress: '*************',
      userAgent: 'APIClient/2.1.0',
      location: 'Frankfurt, DE',
      success: false,
      metadata: {
        apiKey: 'ak_***4567',
        requestCount: 1001,
        timeWindow: '1 hour',
        endpoint: '/api/v1/datasets'
      }
    },
    {
      id: '8',
      timestamp: '2024-01-15 13:55:45',
      userId: 'user_999',
      userName: 'Test User',
      userEmail: '<EMAIL>',
      action: 'Account Deletion',
      category: 'user_management',
      severity: 'high',
      resource: '/admin/users/delete',
      details: 'User account permanently deleted by admin',
      ipAddress: '*********',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      location: 'San Francisco, US',
      success: true,
      metadata: {
        deletedUserId: 'user_888',
        reason: 'User request',
        dataRetention: '30 days'
      }
    }
  ]);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleBackToAdmin = () => {
    navigate('/app/admin-dashboard');
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleViewDetails = (log: AuditLog) => {
    setSelectedLog(log);
    setOpenDetailsDialog(true);
  };

  const handleToggleRowExpansion = (logId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(logId)) {
      newExpanded.delete(logId);
    } else {
      newExpanded.add(logId);
    }
    setExpandedRows(newExpanded);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleExportLogs = () => {
    console.log('Exporting audit logs...');
  };

  const handleRefreshLogs = () => {
    console.log('Refreshing audit logs...');
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'authentication':
        return <Login />;
      case 'user_management':
        return <Person />;
      case 'data_access':
        return <Storage />;
      case 'system_config':
        return <Settings />;
      case 'security':
        return <Security />;
      case 'api_access':
        return <Api />;
      default:
        return <Info />;
    }
  };

  const getDeviceIcon = (userAgent: string) => {
    if (userAgent.includes('Mobile') || userAgent.includes('iPhone') || userAgent.includes('Android')) {
      return <PhoneAndroid />;
    } else if (userAgent.includes('Mozilla')) {
      return <Web />;
    } else {
      return <Computer />;
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const filteredLogs = auditLogs.filter(log => {
    const matchesSearch = log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.details.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || log.category === filterCategory;
    const matchesSeverity = filterSeverity === 'all' || log.severity === filterSeverity;
    
    return matchesSearch && matchesCategory && matchesSeverity;
  });

  const paginatedLogs = filteredLogs.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
        transition: 'all 0.3s ease-in-out',
        ...(isMaximized && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
          bgcolor: 'background.paper'
        })
      }}
    >
      <Container
        maxWidth={isMaximized ? false : 'xl'}
        sx={{
          py: 3,
          px: isMaximized ? 3 : undefined,
          height: isMaximized ? '100vh' : 'auto',
          overflow: isMaximized ? 'auto' : 'visible'
        }}
      >
        <PageTitle
          title="Audit Logs"
          description="Monitor system activities, user actions, and security events"
          icon={<History />}
          breadcrumbs={[
            {
              label: 'Admin Dashboard',
              onClick: handleBackToAdmin
            },
            {
              label: 'Audit Logs'
            }
          ]}
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Export Logs">
                <IconButton
                  onClick={handleExportLogs}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  <Download />
                </IconButton>
              </Tooltip>
              <Tooltip title="Refresh Logs">
                <IconButton
                  onClick={handleRefreshLogs}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  <Refresh />
                </IconButton>
              </Tooltip>
              <Tooltip title={isMaximized ? 'Minimize' : 'Maximize'}>
                <IconButton
                  onClick={handleToggleMaximize}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  {isMaximized ? <FullscreenExit /> : <FullscreenIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          }
        />

        {/* Statistics Overview */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary.main" sx={{ fontWeight: 600 }}>
                  {formatNumber(auditStats.totalLogs)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Logs
                </Typography>
                <History color="primary" sx={{ mt: 1 }} />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
                  {formatNumber(auditStats.todayLogs)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Today's Logs
                </Typography>
                <Event color="info" sx={{ mt: 1 }} />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                  {formatNumber(auditStats.failedAttempts)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Failed Attempts
                </Typography>
                <Warning color="warning" sx={{ mt: 1 }} />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="error.main" sx={{ fontWeight: 600 }}>
                  {formatNumber(auditStats.criticalEvents)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Critical Events
                </Typography>
                <Error color="error" sx={{ mt: 1 }} />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                  {formatNumber(auditStats.uniqueUsers)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Unique Users
                </Typography>
                <Person color="success" sx={{ mt: 1 }} />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="secondary.main" sx={{ fontWeight: 600 }}>
                  {auditStats.topActions.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Action Types
                </Typography>
                <Assessment color="secondary" sx={{ mt: 1 }} />
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters and Actions */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                size="small"
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Category</InputLabel>
                <Select
                  value={filterCategory}
                  label="Category"
                  onChange={(e) => setFilterCategory(e.target.value)}
                >
                  <MenuItem value="all">All Categories</MenuItem>
                  <MenuItem value="authentication">Authentication</MenuItem>
                  <MenuItem value="user_management">User Management</MenuItem>
                  <MenuItem value="data_access">Data Access</MenuItem>
                  <MenuItem value="system_config">System Config</MenuItem>
                  <MenuItem value="security">Security</MenuItem>
                  <MenuItem value="api_access">API Access</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Severity</InputLabel>
                <Select
                  value={filterSeverity}
                  label="Severity"
                  onChange={(e) => setFilterSeverity(e.target.value)}
                >
                  <MenuItem value="all">All Severities</MenuItem>
                  <MenuItem value="critical">Critical</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="low">Low</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Date Range</InputLabel>
                <Select
                  value={filterDateRange}
                  label="Date Range"
                  onChange={(e) => setFilterDateRange(e.target.value)}
                >
                  <MenuItem value="today">Today</MenuItem>
                  <MenuItem value="week">This Week</MenuItem>
                  <MenuItem value="month">This Month</MenuItem>
                  <MenuItem value="quarter">This Quarter</MenuItem>
                  <MenuItem value="year">This Year</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Stack direction="row" spacing={1}>
                <Button
                  variant="outlined"
                  startIcon={<Refresh />}
                  onClick={handleRefreshLogs}
                  size="small"
                >
                  Refresh
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Download />}
                  onClick={handleExportLogs}
                  size="small"
                >
                  Export
                </Button>
              </Stack>
            </Grid>
          </Grid>
        </Paper>

        {/* Audit Logs Table */}
        <Card>
          <CardHeader
            title={`Audit Logs (${formatNumber(filteredLogs.length)} entries)`}
            subheader="System activity and security events"
          />
          <CardContent sx={{ p: 0 }}>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Timestamp</TableCell>
                    <TableCell>User</TableCell>
                    <TableCell>Action</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell>Severity</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paginatedLogs.map((log) => (
                    <React.Fragment key={log.id}>
                      <TableRow hover>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {log.timestamp}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {log.ipAddress}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar sx={{ width: 32, height: 32 }}>
                              {log.userName.charAt(0)}
                            </Avatar>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {log.userName}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {log.userEmail}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getCategoryIcon(log.category)}
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {log.action}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {log.resource}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="small"
                            label={log.category.replace('_', ' ')}
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="small"
                            label={log.severity}
                            color={getSeverityColor(log.severity) as any}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="small"
                            label={log.success ? 'Success' : 'Failed'}
                            color={log.success ? 'success' : 'error'}
                            icon={log.success ? <CheckCircle /> : <Error />}
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LocationOn fontSize="small" color="action" />
                            <Typography variant="body2">
                              {log.location}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <IconButton
                              size="small"
                              onClick={() => handleToggleRowExpansion(log.id)}
                            >
                              {expandedRows.has(log.id) ? <ExpandLess /> : <ExpandMore />}
                            </IconButton>
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleViewDetails(log)}
                            >
                              <Visibility />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell colSpan={8} sx={{ py: 0 }}>
                          <Collapse in={expandedRows.has(log.id)} timeout="auto" unmountOnExit>
                            <Box sx={{ p: 2, bgcolor: 'background.default' }}>
                              <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                    Details
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    {log.details}
                                  </Typography>
                                </Grid>
                                <Grid item xs={12} md={3}>
                                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                    Device Info
                                  </Typography>
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                    {getDeviceIcon(log.userAgent)}
                                    <Typography variant="body2" color="text.secondary">
                                      {log.userAgent.includes('Mobile') ? 'Mobile' : 'Desktop'}
                                    </Typography>
                                  </Box>
                                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                    {log.userAgent.substring(0, 50)}...
                                  </Typography>
                                </Grid>
                                <Grid item xs={12} md={3}>
                                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                    Metadata
                                  </Typography>
                                  {log.metadata && Object.entries(log.metadata).map(([key, value]) => (
                                    <Typography key={key} variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                      <strong>{key}:</strong> {String(value)}
                                    </Typography>
                                  ))}
                                </Grid>
                              </Grid>
                            </Box>
                          </Collapse>
                        </TableCell>
                      </TableRow>
                    </React.Fragment>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[10, 25, 50, 100]}
              component="div"
              count={filteredLogs.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </CardContent>
        </Card>

        {/* Details Dialog */}
        <Dialog
          open={openDetailsDialog}
          onClose={() => setOpenDetailsDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <History />
              Audit Log Details
            </Box>
          </DialogTitle>
          <DialogContent>
            {selectedLog && (
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Timestamp
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedLog.timestamp}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    User
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedLog.userName} ({selectedLog.userEmail})
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Action
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedLog.action}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Category
                  </Typography>
                  <Chip
                    size="small"
                    label={selectedLog.category.replace('_', ' ')}
                    variant="outlined"
                    sx={{ mb: 2 }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Severity
                  </Typography>
                  <Chip
                    size="small"
                    label={selectedLog.severity}
                    color={getSeverityColor(selectedLog.severity) as any}
                    sx={{ mb: 2 }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Status
                  </Typography>
                  <Chip
                    size="small"
                    label={selectedLog.success ? 'Success' : 'Failed'}
                    color={selectedLog.success ? 'success' : 'error'}
                    icon={selectedLog.success ? <CheckCircle /> : <Error />}
                    sx={{ mb: 2 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Details
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedLog.details}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    IP Address
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2, fontFamily: 'monospace' }}>
                    {selectedLog.ipAddress}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Location
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedLog.location}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    User Agent
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2, fontFamily: 'monospace', wordBreak: 'break-all' }}>
                    {selectedLog.userAgent}
                  </Typography>
                </Grid>
                {selectedLog.metadata && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                      Additional Metadata
                    </Typography>
                    <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
                      <pre style={{ margin: 0, fontSize: '0.875rem' }}>
                        {JSON.stringify(selectedLog.metadata, null, 2)}
                      </pre>
                    </Paper>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDetailsDialog(false)}>Close</Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  );
};

export default AuditLogsPage;