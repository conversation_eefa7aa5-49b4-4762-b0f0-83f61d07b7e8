import React, { useState, useEffect, useContext, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  IconButton,
  Button,
  Chip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  Tooltip,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  People as PeopleIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import PageTitle from '../../components/UI/PageTitle';

interface User {
  id: string;
  email: string;
  name: string;
  accountType: 'standard' | 'pro' | 'edu';
  status: 'active' | 'suspended' | 'pending';
  lastLogin: string;
  createdAt: string;
  isAdmin: boolean;
}

interface UserManagementPageProps {}

const UserManagementPage: React.FC<UserManagementPageProps> = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [isMaximized, setIsMaximized] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterAccountType, setFilterAccountType] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState<'edit' | 'delete' | 'suspend'>('edit');

  // Mock data for demonstration
  useEffect(() => {
    const mockUsers: User[] = [
      {
        id: '1',
        email: '<EMAIL>',
        name: 'John Doe',
        accountType: 'pro',
        status: 'active',
        lastLogin: '2024-01-15T10:30:00Z',
        createdAt: '2023-06-01T09:00:00Z',
        isAdmin: false
      },
      {
        id: '2',
        email: '<EMAIL>',
        name: 'Jane Smith',
        accountType: 'edu',
        status: 'active',
        lastLogin: '2024-01-14T14:20:00Z',
        createdAt: '2023-08-15T11:30:00Z',
        isAdmin: false
      },
      {
        id: '3',
        email: '<EMAIL>',
        name: 'System Admin',
        accountType: 'pro',
        status: 'active',
        lastLogin: '2024-01-15T16:45:00Z',
        createdAt: '2023-01-01T00:00:00Z',
        isAdmin: true
      }
    ];
    
    setTimeout(() => {
      setUsers(mockUsers);
      setLoading(false);
    }, 1000);
  }, []);

  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleBackToAdmin = () => {
    navigate('/app/admin-dashboard');
  };

  const getAccountTypeIcon = (type: string) => {
    switch (type) {
      case 'edu': return <School fontSize="small" />;
      case 'pro': return <Business fontSize="small" />;
      default: return <People fontSize="small" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'suspended': return 'error';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || user.status === filterStatus;
    const matchesAccountType = filterAccountType === 'all' || user.accountType === filterAccountType;
    
    return matchesSearch && matchesStatus && matchesAccountType;
  });

  const handleUserAction = (user: User, action: 'edit' | 'delete' | 'suspend') => {
    setSelectedUser(user);
    setDialogType(action);
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedUser(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const userStats = {
    total: users.length,
    active: users.filter(u => u.status === 'active').length,
    suspended: users.filter(u => u.status === 'suspended').length,
    pending: users.filter(u => u.status === 'pending').length
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
        transition: 'all 0.3s ease-in-out',
        ...(isMaximized && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
          bgcolor: 'background.paper'
        })
      }}
    >
      <Container
        maxWidth={isMaximized ? false : 'xl'}
        sx={{
          py: 3,
          px: isMaximized ? 3 : undefined,
          height: isMaximized ? '100vh' : 'auto',
          overflow: isMaximized ? 'auto' : 'visible'
        }}
      >
        <PageTitle
          title="User Management"
          description="Manage user accounts, permissions, and access controls"
          icon={<PeopleIcon />}
          breadcrumbs={[
            {
              label: 'Admin Dashboard',
              onClick: () => navigate('/app/admin-dashboard')
            },
            {
              label: 'User Management'
            }
          ]}
          action={
            <Tooltip title={isMaximized ? 'Minimize' : 'Maximize'}>
              <IconButton
                onClick={handleToggleMaximize}
                sx={{
                  bgcolor: 'action.hover',
                  '&:hover': { bgcolor: 'action.selected' }
                }}
              >
                {isMaximized ? <FullscreenExitIcon /> : <FullscreenIcon />}
              </IconButton>
            </Tooltip>
          }
        />

        {/* User Statistics */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <People sx={{ fontSize: 40, color: 'primary.main' }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {userStats.total}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Users
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <CheckCircle sx={{ fontSize: 40, color: 'success.main' }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {userStats.active}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active Users
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Block sx={{ fontSize: 40, color: 'error.main' }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {userStats.suspended}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Suspended
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <PersonAdd sx={{ fontSize: 40, color: 'warning.main' }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {userStats.pending}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pending
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters and Search */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filterStatus}
                  label="Status"
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="suspended">Suspended</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Account Type</InputLabel>
                <Select
                  value={filterAccountType}
                  label="Account Type"
                  onChange={(e) => setFilterAccountType(e.target.value)}
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="standard">Standard</MenuItem>
                  <MenuItem value="pro">Pro</MenuItem>
                  <MenuItem value="edu">Educational</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<Add />}
                onClick={() => handleUserAction({} as User, 'edit')}
              >
                Add User
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Users Table */}
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Account Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Login</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography>Loading users...</Typography>
                    </TableCell>
                  </TableRow>
                ) : filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography>No users found</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id} hover>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {user.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {user.email}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getAccountTypeIcon(user.accountType)}
                          <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                            {user.accountType}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.status}
                          color={getStatusColor(user.status) as any}
                          size="small"
                          sx={{ textTransform: 'capitalize' }}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(user.lastLogin)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(user.createdAt)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {user.isAdmin && (
                          <Chip
                            icon={<AdminPanelSettings />}
                            label="Admin"
                            color="primary"
                            size="small"
                          />
                        )}
                      </TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="Edit User">
                            <IconButton
                              size="small"
                              onClick={() => handleUserAction(user, 'edit')}
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Suspend User">
                            <IconButton
                              size="small"
                              onClick={() => handleUserAction(user, 'suspend')}
                              disabled={user.isAdmin}
                            >
                              <Block fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete User">
                            <IconButton
                              size="small"
                              onClick={() => handleUserAction(user, 'delete')}
                              disabled={user.isAdmin}
                              sx={{ color: 'error.main' }}
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>

        {/* Action Dialog */}
        <Dialog open={dialogOpen} onClose={handleDialogClose} maxWidth="sm" fullWidth>
          <DialogTitle>
            {dialogType === 'edit' && 'Edit User'}
            {dialogType === 'delete' && 'Delete User'}
            {dialogType === 'suspend' && 'Suspend User'}
          </DialogTitle>
          <DialogContent>
            {dialogType === 'edit' && (
              <Alert severity="info" sx={{ mt: 1 }}>
                User editing functionality would be implemented here.
              </Alert>
            )}
            {dialogType === 'delete' && (
              <Alert severity="warning" sx={{ mt: 1 }}>
                Are you sure you want to delete {selectedUser?.name}? This action cannot be undone.
              </Alert>
            )}
            {dialogType === 'suspend' && (
              <Alert severity="warning" sx={{ mt: 1 }}>
                Are you sure you want to suspend {selectedUser?.name}? They will lose access to the system.
              </Alert>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDialogClose}>Cancel</Button>
            <Button
              variant="contained"
              color={dialogType === 'delete' ? 'error' : 'primary'}
              onClick={handleDialogClose}
            >
              {dialogType === 'edit' && 'Save Changes'}
              {dialogType === 'delete' && 'Delete'}
              {dialogType === 'suspend' && 'Suspend'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  );
};

export default UserManagementPage;