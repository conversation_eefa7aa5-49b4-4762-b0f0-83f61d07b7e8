import React, { useState, useEffect, useContext, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  IconButton,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Alert,
  LinearProgress,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import {
  Search as SearchIcon,
  Edit as EditIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Storage as StorageIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import PageTitle from '../../components/UI/PageTitle';

interface UserQuota {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  accountType: 'standard' | 'pro' | 'edu';
  maxDatasets: number;
  currentDatasets: number;
  maxStorageMB: number;
  currentStorageMB: number;
  lastUpdated: string;
  status: 'normal' | 'warning' | 'exceeded';
}

interface DatasetQuotaPageProps {}

const DatasetQuotaPage: React.FC<DatasetQuotaPageProps> = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [isMaximized, setIsMaximized] = useState(false);
  const [quotas, setQuotas] = useState<UserQuota[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAccountType, setFilterAccountType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [selectedQuota, setSelectedQuota] = useState<UserQuota | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editValues, setEditValues] = useState({ maxDatasets: 0, maxStorageMB: 0 });

  // Mock data for demonstration
  useEffect(() => {
    const mockQuotas: UserQuota[] = [
      {
        id: '1',
        userId: '1',
        userName: 'John Doe',
        userEmail: '<EMAIL>',
        accountType: 'pro',
        maxDatasets: 50,
        currentDatasets: 35,
        maxStorageMB: 5000,
        currentStorageMB: 3200,
        lastUpdated: '2024-01-15T10:30:00Z',
        status: 'normal'
      },
      {
        id: '2',
        userId: '2',
        userName: 'Jane Smith',
        userEmail: '<EMAIL>',
        accountType: 'edu',
        maxDatasets: 100,
        currentDatasets: 85,
        maxStorageMB: 10000,
        currentStorageMB: 8500,
        lastUpdated: '2024-01-14T14:20:00Z',
        status: 'warning'
      },
      {
        id: '3',
        userId: '3',
        userName: 'Bob Wilson',
        userEmail: '<EMAIL>',
        accountType: 'standard',
        maxDatasets: 10,
        currentDatasets: 12,
        maxStorageMB: 1000,
        currentStorageMB: 1200,
        lastUpdated: '2024-01-13T09:15:00Z',
        status: 'exceeded'
      }
    ];
    
    setTimeout(() => {
      setQuotas(mockQuotas);
      setLoading(false);
    }, 1000);
  }, []);

  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleBackToAdmin = () => {
    navigate('/app/admin-dashboard');
  };

  const getAccountTypeIcon = (type: string) => {
    switch (type) {
      case 'edu': return <School fontSize="small" />;
      case 'pro': return <Business fontSize="small" />;
      default: return <Person fontSize="small" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'success';
      case 'warning': return 'warning';
      case 'exceeded': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal': return <CheckCircle fontSize="small" />;
      case 'warning': return <Warning fontSize="small" />;
      case 'exceeded': return <Warning fontSize="small" />;
      default: return null;
    }
  };

  const calculateUsagePercentage = (current: number, max: number) => {
    return Math.min((current / max) * 100, 100);
  };

  const formatStorage = (mb: number) => {
    if (mb >= 1000) {
      return `${(mb / 1000).toFixed(1)} GB`;
    }
    return `${mb} MB`;
  };

  const filteredQuotas = quotas.filter(quota => {
    const matchesSearch = quota.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quota.userEmail.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesAccountType = filterAccountType === 'all' || quota.accountType === filterAccountType;
    const matchesStatus = filterStatus === 'all' || quota.status === filterStatus;
    
    return matchesSearch && matchesAccountType && matchesStatus;
  });

  const handleEditQuota = (quota: UserQuota) => {
    setSelectedQuota(quota);
    setEditValues({
      maxDatasets: quota.maxDatasets,
      maxStorageMB: quota.maxStorageMB
    });
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedQuota(null);
  };

  const handleSaveQuota = () => {
    if (selectedQuota) {
      const updatedQuotas = quotas.map(quota => 
        quota.id === selectedQuota.id 
          ? { 
              ...quota, 
              maxDatasets: editValues.maxDatasets,
              maxStorageMB: editValues.maxStorageMB,
              lastUpdated: new Date().toISOString(),
              status: quota.currentDatasets > editValues.maxDatasets || quota.currentStorageMB > editValues.maxStorageMB 
                ? 'exceeded' 
                : quota.currentDatasets / editValues.maxDatasets > 0.8 || quota.currentStorageMB / editValues.maxStorageMB > 0.8
                ? 'warning'
                : 'normal'
            }
          : quota
      );
      setQuotas(updatedQuotas);
    }
    handleDialogClose();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const quotaStats = {
    total: quotas.length,
    normal: quotas.filter(q => q.status === 'normal').length,
    warning: quotas.filter(q => q.status === 'warning').length,
    exceeded: quotas.filter(q => q.status === 'exceeded').length,
    totalStorage: quotas.reduce((sum, q) => sum + q.currentStorageMB, 0),
    totalDatasets: quotas.reduce((sum, q) => sum + q.currentDatasets, 0)
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
        transition: 'all 0.3s ease-in-out',
        ...(isMaximized && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
          bgcolor: 'background.paper'
        })
      }}
    >
      <Container
        maxWidth={isMaximized ? false : 'xl'}
        sx={{
          py: 3,
          px: isMaximized ? 3 : undefined,
          height: isMaximized ? '100vh' : 'auto',
          overflow: isMaximized ? 'auto' : 'visible'
        }}
      >
        <PageTitle
          title="Dataset Quota Management"
          description="Manage dataset quotas and limits for users and organizations"
          icon={<StorageIcon />}
          breadcrumbs={[
            {
              label: 'Admin Dashboard',
              onClick: () => navigate('/app/admin-dashboard')
            },
            {
              label: 'Dataset Quota Management'
            }
          ]}
          action={
            <Tooltip title={isMaximized ? 'Minimize' : 'Maximize'}>
              <IconButton
                onClick={handleToggleMaximize}
                sx={{
                  bgcolor: 'action.hover',
                  '&:hover': { bgcolor: 'action.selected' }
                }}
              >
                {isMaximized ? <FullscreenExitIcon /> : <FullscreenIcon />}
              </IconButton>
            </Tooltip>
          }
        />

        {/* Quota Statistics */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Storage sx={{ fontSize: 40, color: 'primary.main' }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {quotaStats.total}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Users
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <DataUsage sx={{ fontSize: 40, color: 'success.main' }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {quotaStats.totalDatasets}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Datasets
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <CloudUpload sx={{ fontSize: 40, color: 'info.main' }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {formatStorage(quotaStats.totalStorage)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Storage
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Warning sx={{ fontSize: 40, color: 'error.main' }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {quotaStats.exceeded}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Over Quota
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters and Search */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Account Type</InputLabel>
                <Select
                  value={filterAccountType}
                  label="Account Type"
                  onChange={(e) => setFilterAccountType(e.target.value)}
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="standard">Standard</MenuItem>
                  <MenuItem value="pro">Pro</MenuItem>
                  <MenuItem value="edu">Educational</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filterStatus}
                  label="Status"
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="normal">Normal</MenuItem>
                  <MenuItem value="warning">Warning</MenuItem>
                  <MenuItem value="exceeded">Exceeded</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Paper>

        {/* Quotas Table */}
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Account Type</TableCell>
                  <TableCell>Dataset Usage</TableCell>
                  <TableCell>Storage Usage</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Updated</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography>Loading quotas...</Typography>
                    </TableCell>
                  </TableRow>
                ) : filteredQuotas.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography>No quotas found</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredQuotas.map((quota) => (
                    <TableRow key={quota.id} hover>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {quota.userName}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {quota.userEmail}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getAccountTypeIcon(quota.accountType)}
                          <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                            {quota.accountType}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ minWidth: 120 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2">
                              {quota.currentDatasets}/{quota.maxDatasets}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {Math.round(calculateUsagePercentage(quota.currentDatasets, quota.maxDatasets))}%
                            </Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={calculateUsagePercentage(quota.currentDatasets, quota.maxDatasets)}
                            color={quota.currentDatasets > quota.maxDatasets ? 'error' : 
                                   quota.currentDatasets / quota.maxDatasets > 0.8 ? 'warning' : 'primary'}
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ minWidth: 120 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2">
                              {formatStorage(quota.currentStorageMB)}/{formatStorage(quota.maxStorageMB)}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {Math.round(calculateUsagePercentage(quota.currentStorageMB, quota.maxStorageMB))}%
                            </Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={calculateUsagePercentage(quota.currentStorageMB, quota.maxStorageMB)}
                            color={quota.currentStorageMB > quota.maxStorageMB ? 'error' : 
                                   quota.currentStorageMB / quota.maxStorageMB > 0.8 ? 'warning' : 'primary'}
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getStatusIcon(quota.status)}
                          label={quota.status}
                          color={getStatusColor(quota.status) as any}
                          size="small"
                          sx={{ textTransform: 'capitalize' }}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(quota.lastUpdated)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Tooltip title="Edit Quota">
                          <IconButton
                            size="small"
                            onClick={() => handleEditQuota(quota)}
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>

        {/* Edit Quota Dialog */}
        <Dialog open={dialogOpen} onClose={handleDialogClose} maxWidth="sm" fullWidth>
          <DialogTitle>
            Edit Quota for {selectedQuota?.userName}
          </DialogTitle>
          <DialogContent sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Maximum Datasets: {editValues.maxDatasets}
                </Typography>
                <Slider
                  value={editValues.maxDatasets}
                  onChange={(_, value) => setEditValues(prev => ({ ...prev, maxDatasets: value as number }))}
                  min={1}
                  max={200}
                  step={1}
                  marks={[
                    { value: 10, label: '10' },
                    { value: 50, label: '50' },
                    { value: 100, label: '100' },
                    { value: 200, label: '200' }
                  ]}
                  valueLabelDisplay="auto"
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Maximum Storage: {formatStorage(editValues.maxStorageMB)}
                </Typography>
                <Slider
                  value={editValues.maxStorageMB}
                  onChange={(_, value) => setEditValues(prev => ({ ...prev, maxStorageMB: value as number }))}
                  min={100}
                  max={50000}
                  step={100}
                  marks={[
                    { value: 1000, label: '1GB' },
                    { value: 5000, label: '5GB' },
                    { value: 10000, label: '10GB' },
                    { value: 25000, label: '25GB' },
                    { value: 50000, label: '50GB' }
                  ]}
                  valueLabelDisplay="auto"
                  valueLabelFormat={(value) => formatStorage(value)}
                />
              </Grid>
              {selectedQuota && (
                <Grid item xs={12}>
                  <Alert severity="info">
                    Current usage: {selectedQuota.currentDatasets} datasets, {formatStorage(selectedQuota.currentStorageMB)} storage
                  </Alert>
                </Grid>
              )}
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDialogClose}>Cancel</Button>
            <Button variant="contained" onClick={handleSaveQuota}>
              Save Changes
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  );
};

export default DatasetQuotaPage;